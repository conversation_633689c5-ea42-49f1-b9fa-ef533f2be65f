import { motion } from 'framer-motion';
import { FaExternalLinkAlt, FaGithub, FaRobot, FaShoppingCart, FaGlobe } from 'react-icons/fa';

const Projects = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const projects = [
    {
      title: "AHcorrecting",
      description: "An innovative AI-powered tool that automatically grades students' handwritten answers using OCR technology and <PERSON><PERSON> backend. This project demonstrates the intersection of education technology and artificial intelligence.",
      icon: <FaRobot className="text-4xl text-primary-600" />,
      technologies: ["Laravel", "OCR", "AI/ML", "PHP", "MySQL"],
      features: [
        "Handwriting recognition using OCR",
        "Automated grading system",
        "Teacher dashboard for review",
        "Student performance analytics"
      ],
      status: "Completed",
      category: "AI/Education"
    },
    {
      title: "E-commerce Platform",
      description: "A full-stack e-commerce solution built with modern web technologies, featuring user authentication, payment integration, inventory management, and responsive design.",
      icon: <FaShoppingCart className="text-4xl text-primary-600" />,
      technologies: ["React.js", "Laravel", "MySQL", "Tailwind CSS", "Stripe API"],
      features: [
        "User authentication & profiles",
        "Shopping cart & checkout",
        "Payment gateway integration",
        "Admin dashboard",
        "Inventory management"
      ],
      status: "Completed",
      category: "E-commerce"
    },
    {
      title: "Hexila Company Website",
      description: "Professional company website for Hexila, showcasing our software services including web development, logo design, SEO, and digital marketing solutions.",
      icon: <FaGlobe className="text-4xl text-primary-600" />,
      technologies: ["React.js", "Tailwind CSS", "Framer Motion", "SEO"],
      features: [
        "Service portfolio showcase",
        "Client testimonials",
        "Contact forms & CRM integration",
        "SEO optimized",
        "Mobile-first design"
      ],
      status: "Live",
      category: "Business"
    }
  ];

  return (
    <section id="projects" className="section-padding bg-white">
      <motion.div 
        className="container-custom"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        <motion.div variants={itemVariants} className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-dark-800 mb-4">
            Featured <span className="gradient-text">Projects</span>
          </h2>
          <p className="text-xl text-dark-600 max-w-3xl mx-auto">
            Showcasing innovative solutions that combine technical expertise with business value
          </p>
        </motion.div>

        <div className="space-y-12">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <div className="bg-gradient-to-br from-primary-50 to-primary-100 p-8 rounded-2xl">
                  <div className="flex items-center justify-center h-64">
                    {project.icon}
                  </div>
                </div>
              </div>

              <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''} space-y-6`}>
                <div className="flex items-center gap-4">
                  <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                    {project.category}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    project.status === 'Live' 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-blue-100 text-blue-700'
                  }`}>
                    {project.status}
                  </span>
                </div>

                <h3 className="text-3xl font-bold text-dark-800">
                  {project.title}
                </h3>

                <p className="text-lg text-dark-600 leading-relaxed">
                  {project.description}
                </p>

                <div>
                  <h4 className="text-lg font-semibold text-dark-800 mb-3">Key Features:</h4>
                  <ul className="space-y-2">
                    {project.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-dark-600">
                        <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-dark-800 mb-3">Technologies:</h4>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-3 py-1 bg-dark-100 text-dark-700 rounded-lg text-sm font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <a
                    href="mailto:<EMAIL>?subject=Project Demo Request&body=Hi Taha, I'd like to see a demo of your project."
                    className="btn-primary"
                  >
                    <FaExternalLinkAlt className="mr-2" />
                    Request Demo
                  </a>
                  <a
                    href="https://github.com/taha7091"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-secondary"
                  >
                    <FaGithub className="mr-2" />
                    Source Code
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div variants={itemVariants} className="text-center mt-16">
          <p className="text-lg text-dark-600 mb-6">
            Interested in seeing more of my work?
          </p>
          <a
            href="https://github.com/taha7091"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary"
          >
            <FaGithub className="mr-2" />
            View All Projects on GitHub
          </a>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Projects;
