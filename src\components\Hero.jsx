import { motion } from 'framer-motion';
import { FaGith<PERSON>, Fa<PERSON>inkedin, FaEnvelope, FaDownload, FaInstagram } from 'react-icons/fa';

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-primary-900 text-white relative overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-600 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-primary-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <motion.div 
        className="container-custom text-center z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants} className="mb-8">
          <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-primary-500 to-primary-700 p-1">
            <div className="w-full h-full rounded-full bg-dark-800 flex items-center justify-center">
              <span className="text-4xl font-bold text-primary-400">TA</span>
            </div>
          </div>
        </motion.div>

        <motion.h1 
          variants={itemVariants}
          className="text-5xl md:text-7xl font-bold mb-6"
        >
          <span className="block">Taha</span>
          <span className="gradient-text">Alberjawi</span>
        </motion.h1>

        <motion.p 
          variants={itemVariants}
          className="text-xl md:text-2xl text-dark-300 mb-8 max-w-3xl mx-auto"
        >
          Business Information Technology Management Student & Co-founder of Hexila
        </motion.p>

        <motion.p 
          variants={itemVariants}
          className="text-lg text-dark-400 mb-12 max-w-4xl mx-auto leading-relaxed"
        >
          Passionate about combining business insights with technical solutions to build impactful digital products. 
          Specializing in full-stack development, cybersecurity, and data analytics.
        </motion.p>

      <motion.div 
  variants={itemVariants}
  className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
>
  <a
    href="mailto:<EMAIL>?subject=Let's Connect&body=Hi Taha, I'd like to discuss a potential opportunity with you."
    className="btn-primary"
  >
    <FaEnvelope className="mr-2" />
    Get In Touch
  </a>

  <a href="public\resume taha alberjawi.pdf" download>
    <button className="btn-secondary">
      <FaDownload className="mr-2" />
      Download CV
    </button>
  </a>
</motion.div>


        <motion.div 
          variants={itemVariants}
          className="flex justify-center space-x-6"
        >
          <a 
            href="https://github.com/taha7091" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-dark-400 hover:text-primary-400 transition-colors duration-300 text-2xl"
          >
            <FaGithub />
          </a>
          <a 
            href="https://www.linkedin.com/in/taha-alberjawi-5aa95b330/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-dark-400 hover:text-primary-400 transition-colors duration-300 text-2xl"
          >
            <FaLinkedin />
          </a>
          <a
            href="https://instagram.com/taha_._._b"
            target="_blank"
            rel="noopener noreferrer"
            className="text-dark-400 hover:text-primary-400 transition-colors duration-300 text-2xl"
            aria-label="Instagram Profile"
          >
            <FaInstagram />
          </a>
          <a
            href="mailto:<EMAIL>"
            className="text-dark-400 hover:text-primary-400 transition-colors duration-300 text-2xl"
            aria-label="Email Contact"
          >
            <FaEnvelope />
          </a>
        </motion.div>

        {/* Scroll indicator */}
        <motion.div 
          variants={itemVariants}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="w-6 h-10 border-2 border-dark-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-dark-400 rounded-full mt-2 animate-bounce"></div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
