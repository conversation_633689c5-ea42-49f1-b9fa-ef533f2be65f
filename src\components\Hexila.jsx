import { motion } from 'framer-motion';
import { FaCode, FaPalette, FaSearch, FaBullhorn, FaCogs, FaRocket } from 'react-icons/fa';

const Hexila = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const services = [
    {
      icon: <FaCode className="text-4xl text-white" />,
      title: "Website Development",
      description: "Custom web applications built with modern technologies, focusing on performance, scalability, and user experience.",
      features: ["Responsive Design", "E-commerce Solutions", "CMS Development", "API Integration"]
    },
    {
      icon: <FaPalette className="text-4xl text-white" />,
      title: "Logo Design",
      description: "Professional brand identity design that captures your company's essence and creates lasting impressions.",
      features: ["Brand Strategy", "Logo Creation", "Style Guides", "Brand Assets"]
    },
    {
      icon: <FaSearch className="text-4xl text-white" />,
      title: "SEO Optimization",
      description: "Comprehensive SEO strategies to improve your online visibility and drive organic traffic to your website.",
      features: ["Keyword Research", "On-page SEO", "Technical SEO", "Performance Tracking"]
    },
    {
      icon: <FaBullhorn className="text-4xl text-white" />,
      title: "Digital Marketing",
      description: "Strategic digital marketing campaigns that engage your audience and drive business growth.",
      features: ["Social Media Marketing", "Content Strategy", "PPC Campaigns", "Analytics & Reporting"]
    },
    {
      icon: <FaCogs className="text-4xl text-white" />,
      title: "IT Consulting",
      description: "Expert technology consulting to help businesses leverage technology for competitive advantage.",
      features: ["Technology Strategy", "System Integration", "Process Optimization", "Digital Transformation"]
    },
    {
      icon: <FaRocket className="text-4xl text-white" />,
      title: "Startup Solutions",
      description: "End-to-end solutions for startups, from MVP development to scaling your digital presence.",
      features: ["MVP Development", "Business Strategy", "Technology Stack", "Growth Planning"]
    }
  ];

  return (
    <section id="hexila" className="section-padding bg-gradient-to-br from-dark-900 via-dark-800 to-primary-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-64 h-64 bg-primary-500 rounded-full mix-blend-multiply filter blur-xl opacity-10"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-primary-600 rounded-full mix-blend-multiply filter blur-xl opacity-10"></div>
      </div>

      <motion.div 
        className="container-custom relative z-10"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        <motion.div variants={itemVariants} className="text-center mb-16">
          <div className="inline-block mb-6">
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              <span className="gradient-text">Hexila</span> Services
            </h2>
            <p className="text-xl text-dark-300">Co-founded Software Services Company</p>
          </div>
          <p className="text-lg text-dark-400 max-w-4xl mx-auto leading-relaxed">
            At Hexila, we provide comprehensive software services that help businesses establish 
            and grow their digital presence. From concept to launch, we deliver solutions that 
            combine technical excellence with strategic business value.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white/10 backdrop-blur-md border border-white/20 p-6 rounded-xl card-hover"
            >
              <div className="bg-gradient-to-r from-primary-500 to-primary-600 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                {service.icon}
              </div>
              
              <h3 className="text-xl font-bold text-white mb-4">
                {service.title}
              </h3>
              
              <p className="text-dark-300 mb-6 leading-relaxed">
                {service.description}
              </p>
              
              <ul className="space-y-2">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-dark-400">
                    <div className="w-1.5 h-1.5 bg-primary-400 rounded-full mr-3"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        <motion.div variants={itemVariants} className="text-center">
          <div className="bg-white/5 backdrop-blur-md border border-white/10 p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-dark-300 mb-6 leading-relaxed">
              Whether you're a startup looking to establish your digital presence or an established 
              business seeking to optimize your technology stack, Hexila has the expertise to help 
              you achieve your goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>?subject=Free Consultation Request&body=Hi Taha, I'm interested in a free consultation for my project. Please let me know your availability."
                className="btn-primary"
              >
                Get Free Consultation
              </a>
              <a
                href="https://hexila.netlify.app"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-secondary border-white/30 text-white hover:bg-white hover:text-dark-800"
              >
                View Our Portfolio
              </a>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hexila;
